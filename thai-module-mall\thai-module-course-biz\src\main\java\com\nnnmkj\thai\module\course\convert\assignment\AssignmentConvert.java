package com.nnnmkj.thai.module.course.convert.assignment;

import com.google.common.collect.Maps;
import com.nnnmkj.thai.framework.common.pojo.PageResult;
import com.nnnmkj.thai.framework.common.util.collection.CollectionUtils;
import com.nnnmkj.thai.module.course.controller.admin.assignment.vo.AssignmentRespVO;
import com.nnnmkj.thai.module.course.controller.app.assignment.vo.AppAssignmentRespVO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignment.AssignmentDO;
import com.nnnmkj.thai.module.course.dal.dataobject.assignmentquestion.AssignmentQuestionDO;
import com.nnnmkj.thai.module.course.dal.dataobject.lesson.LessonDO;
import com.nnnmkj.thai.module.member.api.user.dto.MemberUserRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Mapper
public interface AssignmentConvert {

    AssignmentConvert INSTANCE = Mappers.getMapper(AssignmentConvert.class);

    PageResult<AssignmentRespVO> convertPage(PageResult<AssignmentDO> page);

    default PageResult<AssignmentRespVO> convertPage(PageResult<AssignmentDO> page, Map<Long, LessonDO> lessonMap, Map<Long, MemberUserRespDTO> userMap) {
        PageResult<AssignmentRespVO> result = convertPage(page);

        // 填充数据
        result.getList().forEach(vo -> {
            LessonDO lesson = lessonMap.get(vo.getCourseId());
            if (lesson != null) vo.setCourseTitle(lesson.getTitle());
            MemberUserRespDTO user = userMap.get(vo.getUserId());
            if (user != null) vo.setNickname(user.getNickname());
        });

        return result;
    }

    PageResult<AppAssignmentRespVO> convertPage2(PageResult<AssignmentDO> page);

    default PageResult<AppAssignmentRespVO> convertPage3(PageResult<AssignmentDO> page, List<AssignmentQuestionDO> questions) {
        PageResult<AppAssignmentRespVO> result = convertPage2(page);

        // 计算每个作业的题目总分
        Map<Long, List<Double>> assignmentScoreMap = CollectionUtils.convertMultiMap(
                questions, AssignmentQuestionDO::getAssignmentId, AssignmentQuestionDO::getScore
        );

        Map<Long, Double> assignmentTotalScoreMap = Maps.transformValues(
                assignmentScoreMap, scores -> scores.stream()
                        .filter(Objects::nonNull)
                        .mapToDouble(Double::doubleValue)
                        .sum()
        );

        // 设置总分到返回结果中
        result.getList().forEach(
                vo -> {
                    vo.setFullMarks(assignmentTotalScoreMap.getOrDefault(vo.getId(), 0.0));
                }
        );

        return result;
    }

}